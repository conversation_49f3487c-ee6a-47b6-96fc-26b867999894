/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    
    /* Neutral Colors */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-12: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
    min-height: 100vh;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

.btn-secondary {
    background-color: white;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
    border-color: #dc2626;
}

.btn-toggle {
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-toggle.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Cookie Consent Banner */
.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--gray-800);
    color: white;
    padding: var(--spacing-4);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.cookie-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.cookie-actions {
    display: flex;
    gap: var(--spacing-2);
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background-color: white;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--spacing-6) var(--spacing-4);
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.app-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.app-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

/* Controls Panel */
.controls-panel {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-6);
    align-items: end;
}

.search-section {
    display: flex;
    gap: var(--spacing-3);
}

.search-input,
.category-filter {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.search-input:focus,
.category-filter:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.customization-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.size-control {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.size-control label {
    font-size: var(--font-size-xs);
    font-weight: 500;
    color: var(--gray-600);
}

.slider {
    width: 120px;
    height: 4px;
    border-radius: var(--radius-sm);
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

.color-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.color-pickers {
    display: flex;
    gap: var(--spacing-1);
}

.color-pickers input[type="color"] {
    width: 32px;
    height: 32px;
    border: 2px solid var(--gray-300);
    border-radius: var(--radius-sm);
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
}

.color-pickers input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

.color-pickers input[type="color"]::-webkit-color-swatch {
    border: none;
    border-radius: calc(var(--radius-sm) - 2px);
}

.action-section {
    display: flex;
    gap: var(--spacing-2);
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-8) var(--spacing-4);
    width: 100%;
}

/* Icons Grid */
.icons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);
}

.icon-item {
    background-color: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    text-align: center;
    transition: all var(--transition-fast);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.icon-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.icon-display {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-3);
    min-height: 48px;
}

.icon-display svg {
    transition: all var(--transition-fast);
}

.icon-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-1);
}

.icon-category {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-transform: capitalize;
}

.icon-actions {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    display: flex;
    gap: var(--spacing-1);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.icon-item:hover .icon-actions {
    opacity: 1;
}

.icon-action-btn {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: var(--radius-sm);
    background-color: var(--gray-800);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    transition: background-color var(--transition-fast);
}

.icon-action-btn:hover {
    background-color: var(--gray-700);
}

.icon-action-btn.copy {
    background-color: var(--primary-color);
}

.icon-action-btn.copy:hover {
    background-color: var(--primary-hover);
}

.icon-action-btn.download {
    background-color: var(--success-color);
}

.icon-action-btn.download:hover {
    background-color: #059669;
}

.icon-action-btn.edit {
    background-color: var(--warning-color);
}

.icon-action-btn.edit:hover {
    background-color: #d97706;
}

.icon-action-btn.delete {
    background-color: var(--danger-color);
}

.icon-action-btn.delete:hover {
    background-color: #dc2626;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

/* Footer */
.app-footer {
    background-color: white;
    border-top: 1px solid var(--gray-200);
    padding: var(--spacing-6) var(--spacing-4);
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.footer-content p {
    margin-bottom: var(--spacing-2);
}

.footer-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer-content a:hover {
    text-decoration: underline;
}

.font-attribution {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .controls-panel {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }
    
    .search-section {
        flex-direction: column;
    }
    
    .customization-section {
        justify-content: space-between;
    }
    
    .action-section {
        justify-content: center;
    }
    
    .icons-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: var(--spacing-3);
    }
    
    .cookie-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .app-header {
        padding: var(--spacing-4) var(--spacing-3);
    }

    .main-content {
        padding: var(--spacing-6) var(--spacing-3);
    }

    .icons-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    }

    .customization-section {
        flex-direction: column;
        align-items: stretch;
    }

    .action-section {
        flex-direction: column;
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modal-content {
    background-color: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-6) var(--spacing-6) var(--spacing-4);
    border-bottom: 1px solid var(--gray-200);
}

.modal-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: var(--spacing-6);
}

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group textarea {
    resize: vertical;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: var(--font-size-xs);
}

.form-actions {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
    margin-top: var(--spacing-6);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-4);
    right: var(--spacing-4);
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.toast {
    background-color: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--danger-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

.toast.info {
    border-left: 4px solid var(--primary-color);
}

.toast-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
}

.toast-icon {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin-top: 2px;
}

.toast-message {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

.toast-close {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    margin-left: var(--spacing-2);
    font-size: var(--font-size-lg);
    line-height: 1;
}

.toast-close:hover {
    color: var(--gray-600);
}

/* Mobile Modal Adjustments */
@media (max-width: 480px) {
    .modal-content {
        width: 95%;
        margin: var(--spacing-4);
    }

    .modal-header,
    .modal-body {
        padding: var(--spacing-4);
    }

    .form-actions {
        flex-direction: column;
    }

    .toast-container {
        left: var(--spacing-4);
        right: var(--spacing-4);
    }

    .toast {
        min-width: auto;
    }
}
