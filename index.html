<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Comprehensive SVG Icon Management Application - Create, customize, and manage SVG icons">
    <meta name="keywords" content="SVG icons, icon management, vector icons, web icons, mobile icons">
    <meta name="author" content="Jermesa Studio">
    <title>SVG Icon Manager - Jermesa Studio</title>
    
    <!-- Google Fonts - Inter (Open Font License) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- <PERSON><PERSON> Consent Banner -->
    <div id="cookieConsent" class="cookie-consent hidden">
        <div class="cookie-content">
            <p>We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.</p>
            <div class="cookie-actions">
                <button id="acceptCookies" class="btn btn-primary">Accept</button>
                <button id="declineCookies" class="btn btn-secondary">Decline</button>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">SVG Icon Manager</h1>
                <p class="app-subtitle">Create, customize, and manage your SVG icon library</p>
            </div>
            
            <!-- Controls Panel -->
            <div class="controls-panel">
                <!-- Search and Filter -->
                <div class="search-section">
                    <input type="text" id="searchInput" placeholder="Search icons..." class="search-input">
                    <select id="categoryFilter" class="category-filter">
                        <option value="">All Categories</option>
                        <option value="navigation">Navigation</option>
                        <option value="communication">Communication</option>
                        <option value="media">Media</option>
                        <option value="files">Files</option>
                        <option value="settings">Settings</option>
                        <option value="social">Social</option>
                        <option value="business">Business</option>
                        <option value="ui">UI Elements</option>
                        <option value="arrows">Arrows</option>
                        <option value="weather">Weather</option>
                    </select>
                </div>

                <!-- Customization Controls -->
                <div class="customization-section">
                    <div class="size-control">
                        <label for="sizeSlider">Size: <span id="sizeValue">32</span>px</label>
                        <input type="range" id="sizeSlider" min="16" max="128" value="32" class="slider">
                    </div>
                    
                    <div class="color-controls">
                        <button id="colorModeToggle" class="btn btn-toggle">Multi-Color</button>
                        <div class="color-pickers">
                            <input type="color" id="color1" value="#333333" title="Primary Color">
                            <input type="color" id="color2" value="#666666" title="Secondary Color" class="hidden">
                            <input type="color" id="color3" value="#999999" title="Tertiary Color" class="hidden">
                            <input type="color" id="color4" value="#cccccc" title="Quaternary Color" class="hidden">
                            <input type="color" id="color5" value="#eeeeee" title="Quinary Color" class="hidden">
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="action-section">
                    <button id="addIconBtn" class="btn btn-primary">Add Icon</button>
                    <button id="exportLibraryBtn" class="btn btn-secondary">Export Library</button>
                    <button id="importLibraryBtn" class="btn btn-secondary">Import Library</button>
                    <input type="file" id="importFileInput" accept=".json" class="hidden">
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Icons Grid -->
            <div id="iconsGrid" class="icons-grid">
                <!-- Icons will be dynamically generated here -->
            </div>

            <!-- Loading State -->
            <div id="loadingState" class="loading-state">
                <p>Loading icons...</p>
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="empty-state hidden">
                <p>No icons found. Try adjusting your search or add new icons.</p>
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <p>Created by <a href="https://www.jermesa.com" target="_blank" rel="noopener">Jermesa Studio</a></p>
                <p><a href="https://jermesa.com/privacy-policy/" target="_blank" rel="noopener">Privacy Policy</a></p>
                <p class="font-attribution">Font: Inter by Rasmus Andersson (Open Font License)</p>
            </div>
        </footer>
    </div>

    <!-- Modals -->
    <!-- Add/Edit Icon Modal -->
    <div id="iconModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add New Icon</h2>
                <button id="closeModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <form id="iconForm">
                    <div class="form-group">
                        <label for="iconName">Icon Name:</label>
                        <input type="text" id="iconName" required>
                    </div>
                    <div class="form-group">
                        <label for="iconCategory">Category:</label>
                        <select id="iconCategory" required>
                            <option value="navigation">Navigation</option>
                            <option value="communication">Communication</option>
                            <option value="media">Media</option>
                            <option value="files">Files</option>
                            <option value="settings">Settings</option>
                            <option value="social">Social</option>
                            <option value="business">Business</option>
                            <option value="ui">UI Elements</option>
                            <option value="arrows">Arrows</option>
                            <option value="weather">Weather</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="iconSvg">SVG Code:</label>
                        <textarea id="iconSvg" rows="8" placeholder="Paste your SVG code here..." required></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Save Icon</button>
                        <button type="button" id="cancelModal" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Confirm Action</h2>
                <button id="closeConfirmModal" class="close-btn">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to perform this action?</p>
                <div class="form-actions">
                    <button id="confirmAction" class="btn btn-danger">Confirm</button>
                    <button id="cancelConfirm" class="btn btn-secondary">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- JavaScript -->
    <script src="script.js"></script>
</body>
</html>
